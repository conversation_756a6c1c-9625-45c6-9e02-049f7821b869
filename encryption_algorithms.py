# encryption_algorithms.py
# دعم كامل للعربية والإنجليزية في كل الخوارزميات

import numpy as np

# الأبجدية العربية (38 حرفًا)
ARABIC_ALPHABET = "ابتثجحخدذرزسشصضطظعغفقكلمنهويءآأإة"

# الأبجدية الإنجليزية (26 حرفًا)
ENGLISH_ALPHABET = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"

# ==================== CAESAR ====================
def caesar_encrypt(text, shift=3, lang='english'):
    if lang == 'arabic':
        alphabet = ARABIC_ALPHABET
    else:
        alphabet = ENGLISH_ALPHABET
        text = text.upper()

    encrypted = ""
    for char in text:
        if char in alphabet:
            idx = (alphabet.index(char) + shift) % len(alphabet)
            encrypted += alphabet[idx]
        else:
            encrypted += char
    return encrypted

def caesar_decrypt(text, shift=3, lang='english'):
    return caesar_encrypt(text, -shift, lang)

# ==================== PLAYFAIR ====================
def _prepare_playfair_key(key, lang='english'):
    if lang == 'arabic':
        alphabet = ARABIC_ALPHABET
    else:
        alphabet = ENGLISH_ALPHABET.replace("J", "")  # Playfair يستبعد J
        key = key.upper().replace("J", "I")

    key_table = ""
    seen = set()
    for c in key:
        if c in alphabet and c not in seen:
            key_table += c
            seen.add(c)
    for c in alphabet:
        if c not in seen:
            key_table += c
            seen.add(c)
    return key_table

def _playfair_process(text, key_table, encrypt=True, lang='english'):
    if lang == 'arabic':
        alphabet = ARABIC_ALPHABET
        fill_char = 'ا'
    else:
        alphabet = ENGLISH_ALPHABET
        text = text.upper().replace("J", "I")
        fill_char = 'X'

    # تنظيف النص
    text = ''.join(c for c in text if c in alphabet)
    if len(text) % 2 != 0:
        text += fill_char

    pairs = [text[i:i+2] for i in range(0, len(text), 2)]
    result = ""

    n = 6 if lang == 'arabic' else 5  # شبكة 6x6 للعربية (36 خانة)، 5x5 للإنجليزية
    size = n * n

    for pair in pairs:
        a, b = pair[0], pair[1]
        if a == b:
            b = fill_char

        try:
            a_idx = key_table.index(a)
            b_idx = key_table.index(b)
        except ValueError:
            result += a + b
            continue

        a_row, a_col = divmod(a_idx, n)
        b_row, b_col = divmod(b_idx, n)

        if a_row == b_row:
            if encrypt:
                a_new = key_table[a_row * n + (a_col + 1) % n]
                b_new = key_table[b_row * n + (b_col + 1) % n]
            else:
                a_new = key_table[a_row * n + (a_col - 1) % n]
                b_new = key_table[b_row * n + (b_col - 1) % n]
        elif a_col == b_col:
            if encrypt:
                a_new = key_table[((a_row + 1) % n) * n + a_col]
                b_new = key_table[((b_row + 1) % n) * n + b_col]
            else:
                a_new = key_table[((a_row - 1) % n) * n + a_col]
                b_new = key_table[((b_row - 1) % n) * n + b_col]
        else:
            a_new = key_table[a_row * n + b_col]
            b_new = key_table[b_row * n + a_col]

        result += a_new + b_new

    return result

def playfair_encrypt(plaintext, key="MONARCHY", lang='english'):
    key_table = _prepare_playfair_key(key, lang)
    return _playfair_process(plaintext, key_table, encrypt=True, lang=lang)

def playfair_decrypt(ciphertext, key="MONARCHY", lang='english'):
    key_table = _prepare_playfair_key(key, lang)
    return _playfair_process(ciphertext, key_table, encrypt=False, lang=lang)

# ==================== AFFINE ====================
def _mod_inverse(a, m):
    for i in range(1, m):
        if (a * i) % m == 1:
            return i
    return None

def affine_encrypt(text, a=5, b=8, lang='english'):
    if lang == 'arabic':
        alphabet = ARABIC_ALPHABET
    else:
        alphabet = ENGLISH_ALPHABET
        text = text.upper()

    m = len(alphabet)
    if _mod_inverse(a, m) is None:
        raise ValueError(f"قيمة a={a} غير صالحة — يجب أن تكون أولية بالنسبة لعدد الأحرف ({m})")

    result = ""
    for char in text:
        if char in alphabet:
            x = alphabet.index(char)
            y = (a * x + b) % m
            result += alphabet[y]
        else:
            result += char
    return result

def affine_decrypt(text, a=5, b=8, lang='english'):
    if lang == 'arabic':
        alphabet = ARABIC_ALPHABET
    else:
        alphabet = ENGLISH_ALPHABET
        text = text.upper()

    m = len(alphabet)
    a_inv = _mod_inverse(a, m)
    if a_inv is None:
        raise ValueError(f"قيمة a={a} غير صالحة — لا يوجد معكوس")

    result = ""
    for char in text:
        if char in alphabet:
            y = alphabet.index(char)
            x = (a_inv * (y - b)) % m
            result += alphabet[x]
        else:
            result += char
    return result

# ==================== VIGENÈRE ====================
def vigenere_encrypt(text, key="KEY", lang='english'):
    if lang == 'arabic':
        alphabet = ARABIC_ALPHABET
    else:
        alphabet = ENGLISH_ALPHABET
        text = text.upper()
        key = key.upper()

    encrypted = ""
    key_index = 0
    key_len = len(key)
    for char in text:
        if char in alphabet:
            shift = alphabet.index(key[key_index % key_len])
            x = alphabet.index(char)
            y = (x + shift) % len(alphabet)
            encrypted += alphabet[y]
            key_index += 1
        else:
            encrypted += char
    return encrypted

def vigenere_decrypt(text, key="KEY", lang='english'):
    if lang == 'arabic':
        alphabet = ARABIC_ALPHABET
    else:
        alphabet = ENGLISH_ALPHABET
        text = text.upper()
        key = key.upper()

    decrypted = ""
    key_index = 0
    key_len = len(key)
    for char in text:
        if char in alphabet:
            shift = alphabet.index(key[key_index % key_len])
            y = alphabet.index(char)
            x = (y - shift) % len(alphabet)
            decrypted += alphabet[x]
            key_index += 1
        else:
            decrypted += char
    return decrypted

# ==================== HILL ====================
def _prepare_hill_text(text, n=2, lang='english'):
    if lang == 'arabic':
        alphabet = ARABIC_ALPHABET
    else:
        alphabet = ENGLISH_ALPHABET
        text = text.upper()

    text = ''.join(c for c in text if c in alphabet)
    while len(text) % n != 0:
        text += 'ا' if lang == 'arabic' else 'X'
    return text, alphabet

def _hill_process(text, key_matrix, decrypt=False, lang='english'):
    text, alphabet = _prepare_hill_text(text, len(key_matrix), lang)
    n = len(key_matrix)
    m = len(alphabet)

    # تحويل النص إلى أرقام
    nums = [alphabet.index(c) for c in text]
    blocks = [nums[i:i+n] for i in range(0, len(nums), n)]

    # تحويل المفتاح لمصفوفة numpy
    key = np.array(key_matrix)

    if decrypt:
        det = int(round(np.linalg.det(key)))
        if det == 0:
            raise ValueError("المصفوفة غير قابلة للعكس — محدد = 0")
        # حساب المعكوس المعياري
        g = np.gcd(det, m)
        if g != 1:
            raise ValueError(f"المحدد {det} ليس أوليًا مع عدد الأحرف {m} — لا يوجد معكوس")
        det_inv = pow(det, -1, m)
        # حساب المصفوفة المصاحبة
        inv_key = np.round(det_inv * np.linalg.det(key) * np.linalg.inv(key)).astype(int) % m
        key = inv_key

    # التشفير/فك التشفير
    result = []
    for block in blocks:
        block = np.array(block)
        res_block = np.dot(key, block) % m
        result.extend(res_block)

    return ''.join(alphabet[i] for i in result)

def hill_encrypt(text, key_matrix=None, lang='english'):
    if key_matrix is None:
        if lang == 'arabic':
            # مصفوفة 2x2 صالحة mod 38 — محددها 11 → له معكوس mod 38
            key_matrix = [[3, 2], [5, 7]]
        else:
            key_matrix = [[2, 3], [1, 4]]
    return _hill_process(text, key_matrix, decrypt=False, lang=lang)

def hill_decrypt(text, key_matrix=None, lang='english'):
    if key_matrix is None:
        if lang == 'arabic':
            key_matrix = [[3, 2], [5, 7]]
        else:
            key_matrix = [[2, 3], [1, 4]]
    return _hill_process(text, key_matrix, decrypt=True, lang=lang)

# ==================== RSE CRYPTO ====================
def rse_crypto_encrypt(text, key="SECRET", lang='english'):
    return vigenere_encrypt(text, key, lang)

def rse_crypto_decrypt(text, key="SECRET", lang='english'):
    return vigenere_decrypt(text, key, lang)