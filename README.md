# 🔐 تطبيق التشفير التعليمي / Educational Encryption Application

## 📋 الوصف / Description

**العربية:**
تطبيق تعليمي لتشفير وفك تشفير النصوص باستخدام خوارزميات التشفير الكلاسيكية. يدعم التطبيق اللغتين العربية والإنجليزية بشكل كامل مع واجهة ديناميكية تتغير حسب اللغة المختارة.

**English:**
An educational application for encrypting and decrypting text using classical encryption algorithms. The application fully supports both Arabic and English languages with a dynamic interface that changes according to the selected language.

## ✨ المميزات / Features

### 🌐 الدعم اللغوي / Language Support
- **واجهة ديناميكية** تتغير بالكامل حسب اللغة المختارة
- **Dynamic interface** that completely changes according to the selected language
- دعم كامل للنصوص العربية والإنجليزية في جميع الخوارزميات
- Full support for Arabic and English texts in all algorithms

### 🔒 خوارزميات التشفير المدعومة / Supported Encryption Algorithms
1. **Caesar Cipher** - تشفير قيصر
2. **Playfair Cipher** - تشفير بلايفير
3. **Affine Cipher** - التشفير الأفيني
4. **Vigenère Cipher** - تشفير فيجينير
5. **Hill Cipher** - تشفير هيل
6. **RSE Crypto** - تشفير RSE

## 🚀 كيفية التشغيل / How to Run

### الطريقة الأولى - PowerShell (الأسهل / Easiest):
```powershell
cd E:\encryption_app
.\run_app.ps1
```

### الطريقة الثانية - Command Prompt:
```cmd
cd E:\encryption_app
run_app.bat
```

### الطريقة الثالثة - يدوياً / Manual:
```powershell
cd E:\encryption_app
.venv\Scripts\activate
pip install -r requirements.txt
python main.py
```

## 📦 المتطلبات / Requirements

- Python 3.7+
- numpy >= 2.3.0
- tkinter (مدمجة مع Python / Built-in with Python)

## 🎯 كيفية الاستخدام / How to Use

1. **اختر اللغة / Select Language**: اختر "Arabic" أو "English" من القائمة المنسدلة
2. **اختر الخوارزمية / Choose Algorithm**: اختر خوارزمية التشفير المطلوبة
3. **أدخل النص / Enter Text**: أدخل النص المراد تشفيره أو فك تشفيره
4. **أدخل المفتاح / Enter Key**: أدخل المفتاح (اختياري - سيتم استخدام مفتاح افتراضي)
5. **اضغط الزر / Click Button**: اضغط "تشفير/Encryption" أو "فك التشفير/Decryption"

## 📁 هيكل المشروع / Project Structure

```
encryption_app/
├── main.py                 # الملف الرئيسي / Main application file
├── encryption_algorithms.py # خوارزميات التشفير / Encryption algorithms
├── requirements.txt        # المكتبات المطلوبة / Required packages
├── run_app.bat            # ملف تشغيل Windows / Windows run script
├── run_app.ps1            # ملف تشغيل PowerShell / PowerShell run script
├── .venv/                 # البيئة الافتراضية / Virtual environment
└── README.md              # هذا الملف / This file
```

## 🔧 استكشاف الأخطاء / Troubleshooting

### مشكلة: ModuleNotFoundError: No module named 'numpy'
**الحل / Solution:**
```powershell
cd E:\encryption_app
.venv\Scripts\activate
pip install numpy
```

### مشكلة: التطبيق لا يفتح / Application doesn't open
**الحل / Solution:**
تأكد من تفعيل البيئة الافتراضية أولاً
Make sure to activate the virtual environment first

## 👨‍💻 المطور / Developer

تم تطوير هذا التطبيق لأغراض تعليمية لفهم خوارزميات التشفير الكلاسيكية.
This application was developed for educational purposes to understand classical encryption algorithms.

## 📄 الترخيص / License

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي.
This project is open source and available for educational use.
