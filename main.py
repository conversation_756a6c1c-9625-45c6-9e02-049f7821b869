# main.py — واجهة محسنة مع دعم كامل للعربية والإنجليزية في كل الخوارزميات

import tkinter as tk
from tkinter import ttk, messagebox
import encryption_algorithms as enc

class EncryptionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تطبيق التشفير التعليمي — دعم عربي وإنجليزي كامل")
        self.root.geometry("1000x720")
        self.root.configure(bg="#f0f2f5")

        # المتغيرات
        self.language = tk.StringVar(value="English")
        self.encryption_type = tk.StringVar(value="caesar")

        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("TLabel", font=("Arial", 11), background="#f0f2f5")
        style.configure("TButton", font=("Arial", 11))
        style.configure("TRadiobutton", font=("Arial", 11))
        style.configure("TEntry", font=("Arial", 11))
        style.configure("TCombobox", font=("Arial", 11))
        style.configure("TLabelframe", font=("Arial", 12, "bold"), background="#f0f2f5")

        # === الإطار الرئيسي ===
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # === العنوان ===
        title_label = tk.Label(
            main_frame,
            text="🔐 تطبيق التشفير التعليمي — يدعم كل الخوارزميات بالعربية والإنجليزية",
            font=("Arial", 16, "bold"),
            bg="#f0f2f5",
            fg="#2c3e50",
            pady=10
        )
        title_label.pack(pady=(0, 20))

        # === إطارين جنبًا إلى جنب ===
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # === الجانب الأيسر: الإدخال ===
        left_frame = ttk.LabelFrame(content_frame, text=" 🧾 إدخال البيانات ", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # --- حقول الإدخال ---
        fields = [
            ("اللغة / Language:", self.language, ["Arabic", "English"]),
            ("النص الأصلي / Plain Text:", None, None),
            ("النص المشفر / Cipher Text:", None, None),
            ("المفتاح / Key:", None, None),
            ("قيمة A / Value of A:", None, None),
        ]

        self.entries = {}

        for i, (label_text, var, values) in enumerate(fields):
            lbl = ttk.Label(left_frame, text=label_text)
            lbl.grid(row=i, column=0, sticky="w", pady=8, padx=5)

            if values:  # Combobox
                combo = ttk.Combobox(left_frame, textvariable=var, values=values, state="readonly", width=38)
                combo.grid(row=i, column=1, sticky="w", pady=8, padx=5)
                self.entries[label_text] = combo
            else:  # Entry
                entry = ttk.Entry(left_frame, width=40, font=("Arial", 11))
                entry.grid(row=i, column=1, sticky="w", pady=8, padx=5)
                self.entries[label_text] = entry

        # ربط المتغيرات
        self.plaintext_entry = self.entries["النص الأصلي / Plain Text:"]
        self.ciphertext_entry = self.entries["النص المشفر / Cipher Text:"]
        self.key_entry = self.entries["المفتاح / Key:"]
        self.a_value_entry = self.entries["قيمة A / Value of A:"]

        # --- منطقة الإخراج ---
        ttk.Label(left_frame, text="📄 النتيجة / Output:").grid(row=6, column=0, sticky="w", pady=(15,5), padx=5)
        self.output_text = tk.Text(left_frame, width=55, height=7, font=("Arial", 11), bg="#ffffff", relief="solid", borderwidth=1)
        self.output_text.grid(row=7, column=0, columnspan=2, sticky="ew", pady=5, padx=5)

        # === الجانب الأيمن: اختيار الخوارزمية ===
        right_frame = ttk.LabelFrame(content_frame, text=" ⚙️ اختر خوارزمية التشفير ", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        algorithms = [
            ("Caesar", "caesar"),
            ("Playfair", "playfair"),
            ("Affine", "affine"),
            ("Vigenère", "vigenere"),
            ("Hill", "hill"),
            ("RSE Crypto", "rse")
        ]

        for i, (name, value) in enumerate(algorithms):
            rb = ttk.Radiobutton(right_frame, text=name, variable=self.encryption_type, value=value)
            rb.grid(row=i, column=0, sticky="w", pady=6, padx=20)

        # === الأزرار في الأسفل ===
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=25)

        decrypt_btn = tk.Button(
            button_frame,
            text="🔓 فك التشفير / Decryption",
            command=self.decrypt,
            bg="#e74c3c",
            fg="white",
            font=("Arial", 12, "bold"),
            width=22,
            height=2,
            relief="flat",
            cursor="hand2",
            bd=0
        )
        decrypt_btn.pack(side=tk.LEFT, padx=20)

        encrypt_btn = tk.Button(
            button_frame,
            text="🔒 التشفير / Encryption",
            command=self.encrypt,
            bg="#27ae60",
            fg="white",
            font=("Arial", 12, "bold"),
            width=22,
            height=2,
            relief="flat",
            cursor="hand2",
            bd=0
        )
        encrypt_btn.pack(side=tk.RIGHT, padx=20)

        # Hover effect
        for btn, color in [(decrypt_btn, "#c0392b"), (encrypt_btn, "#229954")]:
            original_color = btn['bg']
            btn.bind("<Enter>", lambda e, b=btn, c=color: b.config(bg=c))
            btn.bind("<Leave>", lambda e, b=btn, c=original_color: b.config(bg=c))

    def get_params(self):
        lang = "arabic" if self.language.get() == "Arabic" else "english"
        return {
            "algorithm": self.encryption_type.get(),
            "key": self.key_entry.get(),
            "a": self.a_value_entry.get(),
            "plaintext": self.plaintext_entry.get(),
            "ciphertext": self.ciphertext_entry.get(),
            "language": lang
        }

    def encrypt(self):
        params = self.get_params()
        algorithm = params["algorithm"]
        plaintext = params["plaintext"]
        lang = params["language"]

        if not plaintext.strip():
            messagebox.showwarning("تحذير ⚠️", "الرجاء إدخال نص للتشفير.")
            return

        try:
            if algorithm == "caesar":
                cipher = enc.caesar_encrypt(plaintext, lang=lang)
                key = "3"
                a_val = ""

            elif algorithm == "playfair":
                default_key = "ملك" if lang == "arabic" else "MONARCHY"
                key = params["key"] or default_key
                cipher = enc.playfair_encrypt(plaintext, key, lang=lang)
                a_val = ""

            elif algorithm == "affine":
                a = int(params["a"]) if params["a"] else (5 if lang == "english" else 7)
                b = int(params["key"]) if params["key"] else (8 if lang == "english" else 3)
                cipher = enc.affine_encrypt(plaintext, a, b, lang=lang)
                key = f"a={a}, b={b}"
                a_val = str(a)

            elif algorithm == "vigenere":
                default_key = "مفتاح" if lang == "arabic" else "KEY"
                key = params["key"] or default_key
                cipher = enc.vigenere_encrypt(plaintext, key, lang=lang)
                a_val = ""

            elif algorithm == "hill":
                cipher = enc.hill_encrypt(plaintext, lang=lang)
                key = "مصفوفة 2x2" if lang == "arabic" else "Default 2x2 Matrix"
                a_val = ""

            elif algorithm == "rse":
                default_key = "سري" if lang == "arabic" else "SECRET"
                key = params["key"] or default_key
                cipher = enc.rse_crypto_encrypt(plaintext, key, lang=lang)
                a_val = ""

            else:
                messagebox.showerror("خطأ ❌", "الخوارزمية غير مدعومة.")
                return

            # عرض النتائج
            self.ciphertext_entry.delete(0, tk.END)
            self.ciphertext_entry.insert(0, cipher)
            self.key_entry.delete(0, tk.END)
            self.key_entry.insert(0, key)
            self.a_value_entry.delete(0, tk.END)
            self.a_value_entry.insert(0, a_val)
            self.output_text.delete(1.0, tk.END)
            output = f"✅ التشفير ناجح!\nالنص المشفر: {cipher}\nالمفتاح: {key}"
            if a_val:
                output += f"\nقيمة A: {a_val}"
            self.output_text.insert(tk.END, output)

        except Exception as e:
            messagebox.showerror("خطأ في التشفير ❌", f"تفاصيل الخطأ:\n{str(e)}")

    def decrypt(self):
        params = self.get_params()
        algorithm = params["algorithm"]
        ciphertext = params["ciphertext"]
        lang = params["language"]

        if not ciphertext.strip():
            messagebox.showwarning("تحذير ⚠️", "الرجاء إدخال نص لفك التشفير.")
            return

        try:
            if algorithm == "caesar":
                plain = enc.caesar_decrypt(ciphertext, lang=lang)
                key = "3"

            elif algorithm == "playfair":
                default_key = "ملك" if lang == "arabic" else "MONARCHY"
                key = params["key"] or default_key
                plain = enc.playfair_decrypt(ciphertext, key, lang=lang)

            elif algorithm == "affine":
                a = int(params["a"]) if params["a"] else (5 if lang == "english" else 7)
                b = int(params["key"]) if params["key"] else (8 if lang == "english" else 3)
                plain = enc.affine_decrypt(ciphertext, a, b, lang=lang)
                key = f"a={a}, b={b}"

            elif algorithm == "vigenere":
                default_key = "مفتاح" if lang == "arabic" else "KEY"
                key = params["key"] or default_key
                plain = enc.vigenere_decrypt(ciphertext, key, lang=lang)

            elif algorithm == "hill":
                plain = enc.hill_decrypt(ciphertext, lang=lang)
                key = "مصفوفة 2x2" if lang == "arabic" else "Default 2x2 Matrix"

            elif algorithm == "rse":
                default_key = "سري" if lang == "arabic" else "SECRET"
                key = params["key"] or default_key
                plain = enc.rse_crypto_decrypt(ciphertext, key, lang=lang)

            else:
                messagebox.showerror("خطأ ❌", "الخوارزمية غير مدعومة.")
                return

            # عرض النتائج
            self.plaintext_entry.delete(0, tk.END)
            self.plaintext_entry.insert(0, plain)
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(tk.END, f"✅ فك التشفير ناجح!\nالنص الأصلي: {plain}\nالمفتاح: {key}")

        except Exception as e:
            messagebox.showerror("خطأ في فك التشفير ❌", f"تفاصيل الخطأ:\n{str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = EncryptionApp(root)
    root.mainloop()