# main.py — واجهة ديناميكية تدعم التبديل بين العربية والإنجليزية

import tkinter as tk
from tkinter import ttk, messagebox
import encryption_algorithms as enc

class EncryptionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Encryption App")
        self.root.geometry("1000x720")
        self.root.configure(bg="#f0f2f5")

        # المتغيرات
        self.language = tk.StringVar(value="English")
        self.encryption_type = tk.StringVar(value="caesar")

        # خيارات اللغة
        self.lang_texts = {
            "English": {
                "title": "🔐 Educational Encryption App",
                "input_frame": "Input Data",
                "language_label": "Language:",
                "plain_text_label": "Plain Text:",
                "cipher_text_label": "Cipher Text:",
                "key_label": "Key:",
                "a_value_label": "Value of A:",
                "output_label": "Output:",
                "algorithm_frame": "Choose Encryption Algorithm",
                "encrypt_btn": "🔒 Encryption",
                "decrypt_btn": "🔓 Decryption",
                "affine": "Affine",
                "caesar": "Caesar",
                "playfair": "Playfair",
                "vigenere": "Vigenère",
                "hill": "Hill",
                "rse": "RSE Crypto",
                "success_encrypt": "✅ Encryption successful!",
                "success_decrypt": "✅ Decryption successful!",
                "warning_empty": "⚠️ Please enter text.",
                "error_unsupported": "❌ Algorithm not supported."
            },
            "Arabic": {
                "title": "🔐 تطبيق التشفير التعليمي",
                "input_frame": "إدخال البيانات",
                "language_label": "اللغة:",
                "plain_text_label": "النص الأصلي:",
                "cipher_text_label": "النص المشفر:",
                "key_label": "المفتاح:",
                "a_value_label": "قيمة A:",
                "output_label": "النتيجة:",
                "algorithm_frame": "اختر خوارزمية التشفير",
                "encrypt_btn": "🔒 التشفير",
                "decrypt_btn": "🔓 فك التشفير",
                "affine": "Affine",
                "caesar": "Caesar",
                "playfair": "Playfair",
                "vigenere": "Vigenère",
                "hill": "Hill",
                "rse": "RSE Crypto",
                "success_encrypt": "✅ التشفير ناجح!",
                "success_decrypt": "✅ فك التشفير ناجح!",
                "warning_empty": "⚠️ الرجاء إدخال نص.",
                "error_unsupported": "❌ الخوارزمية غير مدعومة."
            }
        }

        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("TLabel", font=("Arial", 11), background="#f0f2f5")
        style.configure("TButton", font=("Arial", 11))
        style.configure("TRadiobutton", font=("Arial", 11))
        style.configure("TEntry", font=("Arial", 11))
        style.configure("TCombobox", font=("Arial", 11))
        style.configure("TLabelframe", font=("Arial", 12, "bold"), background="#f0f2f5")

        # === الإطار الرئيسي ===
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # === العنوان ===
        self.title_label = tk.Label(
            main_frame,
            text=self.lang_texts["English"]["title"],
            font=("Arial", 16, "bold"),
            bg="#f0f2f5",
            fg="#2c3e50",
            pady=10
        )
        self.title_label.pack(pady=(0, 20))

        # === إطارين جنبًا إلى جنب ===
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # === الجانب الأيسر: الإدخال ===
        self.left_frame = ttk.LabelFrame(content_frame, text=self.lang_texts["English"]["input_frame"], padding=15)
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # --- حقول الإدخال ---
        fields = [
            ("language_label", self.language, ["Arabic", "English"]),
            ("plain_text_label", None, None),
            ("cipher_text_label", None, None),
            ("key_label", None, None),
            ("a_value_label", None, None),
        ]

        self.entries = {}

        for i, (label_key, var, values) in enumerate(fields):
            lbl = ttk.Label(self.left_frame, text=self.lang_texts["English"][label_key])
            lbl.grid(row=i, column=0, sticky="w", pady=8, padx=5)

            if values:  # Combobox
                combo = ttk.Combobox(self.left_frame, textvariable=var, values=values, state="readonly", width=38)
                combo.grid(row=i, column=1, sticky="w", pady=8, padx=5)
                self.entries[label_key] = combo
                combo.bind("<<ComboboxSelected>>", self.on_language_change)
            else:  # Entry
                entry = ttk.Entry(self.left_frame, width=40, font=("Arial", 11))
                entry.grid(row=i, column=1, sticky="w", pady=8, padx=5)
                self.entries[label_key] = entry

        # ربط المتغيرات
        self.plaintext_entry = self.entries["plain_text_label"]
        self.ciphertext_entry = self.entries["cipher_text_label"]
        self.key_entry = self.entries["key_label"]
        self.a_value_entry = self.entries["a_value_label"]

        # --- منطقة الإخراج ---
        output_lbl = ttk.Label(self.left_frame, text=self.lang_texts["English"]["output_label"])
        output_lbl.grid(row=6, column=0, sticky="w", pady=(15,5), padx=5)
        self.output_text = tk.Text(self.left_frame, width=55, height=7, font=("Arial", 11), bg="#ffffff", relief="solid", borderwidth=1)
        self.output_text.grid(row=7, column=0, columnspan=2, sticky="ew", pady=5, padx=5)

        # === الجانب الأيمن: اختيار الخوارزمية ===
        self.right_frame = ttk.LabelFrame(content_frame, text=self.lang_texts["English"]["algorithm_frame"], padding=15)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        algorithms = [
            ("caesar", "Caesar"),
            ("playfair", "Playfair"),
            ("affine", "Affine"),
            ("vigenere", "Vigenère"),
            ("hill", "Hill"),
            ("rse", "RSE Crypto")
        ]

        for i, (value, name) in enumerate(algorithms):
            rb = ttk.Radiobutton(self.right_frame, text=name, variable=self.encryption_type, value=value)
            rb.grid(row=i, column=0, sticky="w", pady=6, padx=20)

        # === الأزرار في الأسفل ===
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=25)

        self.decrypt_btn = tk.Button(
            button_frame,
            text=self.lang_texts["English"]["decrypt_btn"],
            command=self.decrypt,
            bg="#e74c3c",
            fg="white",
            font=("Arial", 12, "bold"),
            width=22,
            height=2,
            relief="flat",
            cursor="hand2",
            bd=0
        )
        self.decrypt_btn.pack(side=tk.LEFT, padx=20)

        self.encrypt_btn = tk.Button(
            button_frame,
            text=self.lang_texts["English"]["encrypt_btn"],
            command=self.encrypt,
            bg="#27ae60",
            fg="white",
            font=("Arial", 12, "bold"),
            width=22,
            height=2,
            relief="flat",
            cursor="hand2",
            bd=0
        )
        self.encrypt_btn.pack(side=tk.RIGHT, padx=20)

        # Hover effect
        for btn, color in [(self.decrypt_btn, "#c0392b"), (self.encrypt_btn, "#229954")]:
            original_color = btn['bg']
            btn.bind("<Enter>", lambda e, b=btn, c=color: b.config(bg=c))
            btn.bind("<Leave>", lambda e, b=btn, c=original_color: b.config(bg=c))

        # أول مرة: تحديث اللغة
        self.on_language_change(None)

    def on_language_change(self, event=None):
        lang = self.language.get()
        texts = self.lang_texts[lang]

        # تحديث العنوان
        self.title_label.config(text=texts["title"])

        # تحديث أسماء الحقول
        self.left_frame.config(text=texts["input_frame"])
        self.right_frame.config(text=texts["algorithm_frame"])

        # تحديث النصوص
        self.entries["language_label"].config(text=texts["language_label"])
        self.entries["plain_text_label"].config(text=texts["plain_text_label"])
        self.entries["cipher_text_label"].config(text=texts["cipher_text_label"])
        self.entries["key_label"].config(text=texts["key_label"])
        self.entries["a_value_label"].config(text=texts["a_value_label"])
        output_lbl = self.left_frame.winfo_children()[6]
        output_lbl.config(text=texts["output_label"])

        # تحديث الأزرار
        self.decrypt_btn.config(text=texts["decrypt_btn"])
        self.encrypt_btn.config(text=texts["encrypt_btn"])

        # تحديث خيارات الخوارزميات
        for widget in self.right_frame.winfo_children():
            if isinstance(widget, ttk.Radiobutton):
                alg_name = widget.cget("text")
                if alg_name in texts:
                    widget.config(text=texts[alg_name])

        # تحديث النصوص داخل Output
        self.output_text.delete(1.0, tk.END)

    def get_params(self):
        lang = "arabic" if self.language.get() == "Arabic" else "english"
        return {
            "algorithm": self.encryption_type.get(),
            "key": self.key_entry.get(),
            "a": self.a_value_entry.get(),
            "plaintext": self.plaintext_entry.get(),
            "ciphertext": self.ciphertext_entry.get(),
            "language": lang
        }

    def encrypt(self):
        params = self.get_params()
        algorithm = params["algorithm"]
        plaintext = params["plaintext"]
        lang = params["language"]

        if not plaintext.strip():
            messagebox.showwarning(self.lang_texts[self.language.get()]["warning_empty"], "")
            return

        try:
            if algorithm == "caesar":
                cipher = enc.caesar_encrypt(plaintext, lang=lang)
                key = "3"
                a_val = ""

            elif algorithm == "playfair":
                default_key = "ملك" if lang == "arabic" else "MONARCHY"
                key = params["key"] or default_key
                cipher = enc.playfair_encrypt(plaintext, key, lang=lang)
                a_val = ""

            elif algorithm == "affine":
                a = int(params["a"]) if params["a"] else (5 if lang == "english" else 7)
                b = int(params["key"]) if params["key"] else (8 if lang == "english" else 3)
                cipher = enc.affine_encrypt(plaintext, a, b, lang=lang)
                key = f"a={a}, b={b}"
                a_val = str(a)

            elif algorithm == "vigenere":
                default_key = "مفتاح" if lang == "arabic" else "KEY"
                key = params["key"] or default_key
                cipher = enc.vigenere_encrypt(plaintext, key, lang=lang)
                a_val = ""

            elif algorithm == "hill":
                cipher = enc.hill_encrypt(plaintext, lang=lang)
                key = "مصفوفة 2x2" if lang == "arabic" else "Default 2x2 Matrix"
                a_val = ""

            elif algorithm == "rse":
                default_key = "سري" if lang == "arabic" else "SECRET"
                key = params["key"] or default_key
                cipher = enc.rse_crypto_encrypt(plaintext, key, lang=lang)
                a_val = ""

            else:
                messagebox.showerror(self.lang_texts[self.language.get()]["error_unsupported"], "")
                return

            # عرض النتائج
            self.ciphertext_entry.delete(0, tk.END)
            self.ciphertext_entry.insert(0, cipher)
            self.key_entry.delete(0, tk.END)
            self.key_entry.insert(0, key)
            self.a_value_entry.delete(0, tk.END)
            self.a_value_entry.insert(0, a_val)
            self.output_text.delete(1.0, tk.END)
            output = f"{texts['success_encrypt']}\n{self.lang_texts[self.language.get()]['cipher_text_label']}:{cipher}\n{self.lang_texts[self.language.get()]['key_label']}:{key}"
            if a_val:
                output += f"\n{self.lang_texts[self.language.get()]['a_value_label']}:{a_val}"
            self.output_text.insert(tk.END, output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to encrypt: {str(e)}")

    def decrypt(self):
        params = self.get_params()
        algorithm = params["algorithm"]
        ciphertext = params["ciphertext"]
        lang = params["language"]

        if not ciphertext.strip():
            messagebox.showwarning(self.lang_texts[self.language.get()]["warning_empty"], "")
            return

        try:
            if algorithm == "caesar":
                plain = enc.caesar_decrypt(ciphertext, lang=lang)
                key = "3"

            elif algorithm == "playfair":
                default_key = "ملك" if lang == "arabic" else "MONARCHY"
                key = params["key"] or default_key
                plain = enc.playfair_decrypt(ciphertext, key, lang=lang)

            elif algorithm == "affine":
                a = int(params["a"]) if params["a"] else (5 if lang == "english" else 7)
                b = int(params["key"]) if params["key"] else (8 if lang == "english" else 3)
                plain = enc.affine_decrypt(ciphertext, a, b, lang=lang)
                key = f"a={a}, b={b}"

            elif algorithm == "vigenere":
                default_key = "مفتاح" if lang == "arabic" else "KEY"
                key = params["key"] or default_key
                plain = enc.vigenere_decrypt(ciphertext, key, lang=lang)

            elif algorithm == "hill":
                plain = enc.hill_decrypt(ciphertext, lang=lang)
                key = "مصفوفة 2x2" if lang == "arabic" else "Default 2x2 Matrix"

            elif algorithm == "rse":
                default_key = "سري" if lang == "arabic" else "SECRET"
                key = params["key"] or default_key
                plain = enc.rse_crypto_decrypt(ciphertext, key, lang=lang)

            else:
                messagebox.showerror(self.lang_texts[self.language.get()]["error_unsupported"], "")
                return

            # عرض النتائج
            self.plaintext_entry.delete(0, tk.END)
            self.plaintext_entry.insert(0, plain)
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(tk.END, f"{texts['success_decrypt']}\n{self.lang_texts[self.language.get()]['plain_text_label']}:{plain}\n{self.lang_texts[self.language.get()]['key_label']}:{key}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to decrypt: {str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = EncryptionApp(root)
    root.mainloop()