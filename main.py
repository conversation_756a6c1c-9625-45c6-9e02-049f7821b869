# main.py — Dynamic bilingual interface with full Arabic/English support

import tkinter as tk
from tkinter import ttk, messagebox
import encryption_algorithms as enc

class EncryptionApp:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1000x720")
        self.root.configure(bg="#f0f2f5")

        # Variables
        self.language = tk.StringVar(value="English")
        self.encryption_type = tk.StringVar(value="caesar")

        # Language change callback
        self.language.trace('w', self.on_language_change)

        # Text dictionary for bilingual support
        self.texts = {
            "English": {
                "title": "🔐 Educational Encryption Application",
                "window_title": "Educational Encryption Application - Full Bilingual Support",
                "input_frame": " 📝 Data Input ",
                "algorithm_frame": " ⚙️ Choose Encryption Algorithm ",
                "language_label": "Language:",
                "plaintext_label": "Plain Text:",
                "ciphertext_label": "Cipher Text:",
                "key_label": "Key:",
                "a_value_label": "Value of A:",
                "output_label": "📄 Output:",
                "encrypt_btn": "🔒 Encryption",
                "decrypt_btn": "🔓 Decryption",
                "warning_title": "Warning ⚠️",
                "error_title": "Error ❌",
                "encrypt_warning": "Please enter text to encrypt.",
                "decrypt_warning": "Please enter text to decrypt.",
                "unsupported_algorithm": "Unsupported algorithm.",
                "encrypt_success": "✅ Encryption successful!\nCipher Text: {}\nKey: {}",
                "decrypt_success": "✅ Decryption successful!\nPlain Text: {}\nKey: {}",
                "encrypt_error": "Encryption Error ❌",
                "decrypt_error": "Decryption Error ❌",
                "error_details": "Error details:\n{}"
            },
            "Arabic": {
                "title": "🔐 تطبيق التشفير التعليمي",
                "window_title": "تطبيق التشفير التعليمي — دعم عربي وإنجليزي كامل",
                "input_frame": " 📝 إدخال البيانات ",
                "algorithm_frame": " ⚙️ اختر خوارزمية التشفير ",
                "language_label": "اللغة:",
                "plaintext_label": "النص الأصلي:",
                "ciphertext_label": "النص المشفر:",
                "key_label": "المفتاح:",
                "a_value_label": "قيمة A:",
                "output_label": "📄 النتيجة:",
                "encrypt_btn": "🔒 التشفير",
                "decrypt_btn": "🔓 فك التشفير",
                "warning_title": "تحذير ⚠️",
                "error_title": "خطأ ❌",
                "encrypt_warning": "الرجاء إدخال نص للتشفير.",
                "decrypt_warning": "الرجاء إدخال نص لفك التشفير.",
                "unsupported_algorithm": "الخوارزمية غير مدعومة.",
                "encrypt_success": "✅ التشفير ناجح!\nالنص المشفر: {}\nالمفتاح: {}",
                "decrypt_success": "✅ فك التشفير ناجح!\nالنص الأصلي: {}\nالمفتاح: {}",
                "encrypt_error": "خطأ في التشفير ❌",
                "decrypt_error": "خطأ في فك التشفير ❌",
                "error_details": "تفاصيل الخطأ:\n{}"
            }
        }

        # Initialize UI
        self.setup_ui()

    def on_language_change(self, *args):
        """Called when language selection changes"""
        self.update_ui_language()

    def get_text(self, key):
        """Get text in current language"""
        return self.texts[self.language.get()][key]

    def update_ui_language(self):
        """Update all UI elements to current language"""
        # Update window title
        self.root.title(self.get_text("window_title"))

        # Update title label
        self.title_label.config(text=self.get_text("title"))

        # Update frame labels
        self.left_frame.config(text=self.get_text("input_frame"))
        self.right_frame.config(text=self.get_text("algorithm_frame"))

        # Update field labels
        self.language_label.config(text=self.get_text("language_label"))
        self.plaintext_label.config(text=self.get_text("plaintext_label"))
        self.ciphertext_label.config(text=self.get_text("ciphertext_label"))
        self.key_label.config(text=self.get_text("key_label"))
        self.a_value_label.config(text=self.get_text("a_value_label"))
        self.output_label.config(text=self.get_text("output_label"))

        # Update buttons
        self.encrypt_btn.config(text=self.get_text("encrypt_btn"))
        self.decrypt_btn.config(text=self.get_text("decrypt_btn"))

    def setup_ui(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("TLabel", font=("Arial", 11), background="#f0f2f5")
        style.configure("TButton", font=("Arial", 11))
        style.configure("TRadiobutton", font=("Arial", 11))
        style.configure("TEntry", font=("Arial", 11))
        style.configure("TCombobox", font=("Arial", 11))
        style.configure("TLabelframe", font=("Arial", 12, "bold"), background="#f0f2f5")

        # === Main Frame ===
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # === Title ===
        self.title_label = tk.Label(
            main_frame,
            text=self.get_text("title"),
            font=("Arial", 16, "bold"),
            bg="#f0f2f5",
            fg="#2c3e50",
            pady=10
        )
        self.title_label.pack(pady=(0, 20))

        # === Content Frame ===
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # === Left Frame: Input ===
        self.left_frame = ttk.LabelFrame(content_frame, text=self.get_text("input_frame"), padding=15)
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # --- Input Fields ---
        # Language selection
        self.language_label = ttk.Label(self.left_frame, text=self.get_text("language_label"))
        self.language_label.grid(row=0, column=0, sticky="w", pady=8, padx=5)

        language_combo = ttk.Combobox(self.left_frame, textvariable=self.language, values=["Arabic", "English"], state="readonly", width=38)
        language_combo.grid(row=0, column=1, sticky="w", pady=8, padx=5)

        # Plain text
        self.plaintext_label = ttk.Label(self.left_frame, text=self.get_text("plaintext_label"))
        self.plaintext_label.grid(row=1, column=0, sticky="w", pady=8, padx=5)

        self.plaintext_entry = ttk.Entry(self.left_frame, width=40, font=("Arial", 11))
        self.plaintext_entry.grid(row=1, column=1, sticky="w", pady=8, padx=5)

        # Cipher text
        self.ciphertext_label = ttk.Label(self.left_frame, text=self.get_text("ciphertext_label"))
        self.ciphertext_label.grid(row=2, column=0, sticky="w", pady=8, padx=5)

        self.ciphertext_entry = ttk.Entry(self.left_frame, width=40, font=("Arial", 11))
        self.ciphertext_entry.grid(row=2, column=1, sticky="w", pady=8, padx=5)

        # Key
        self.key_label = ttk.Label(self.left_frame, text=self.get_text("key_label"))
        self.key_label.grid(row=3, column=0, sticky="w", pady=8, padx=5)

        self.key_entry = ttk.Entry(self.left_frame, width=40, font=("Arial", 11))
        self.key_entry.grid(row=3, column=1, sticky="w", pady=8, padx=5)

        # A value
        self.a_value_label = ttk.Label(self.left_frame, text=self.get_text("a_value_label"))
        self.a_value_label.grid(row=4, column=0, sticky="w", pady=8, padx=5)

        self.a_value_entry = ttk.Entry(self.left_frame, width=40, font=("Arial", 11))
        self.a_value_entry.grid(row=4, column=1, sticky="w", pady=8, padx=5)

        # --- Output Area ---
        self.output_label = ttk.Label(self.left_frame, text=self.get_text("output_label"))
        self.output_label.grid(row=5, column=0, sticky="w", pady=(15,5), padx=5)

        self.output_text = tk.Text(self.left_frame, width=55, height=7, font=("Arial", 11), bg="#ffffff", relief="solid", borderwidth=1)
        self.output_text.grid(row=6, column=0, columnspan=2, sticky="ew", pady=5, padx=5)

        # === Right Frame: Algorithm Selection ===
        self.right_frame = ttk.LabelFrame(content_frame, text=self.get_text("algorithm_frame"), padding=15)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        algorithms = [
            ("Caesar", "caesar"),
            ("Playfair", "playfair"),
            ("Affine", "affine"),
            ("Vigenère", "vigenere"),
            ("Hill", "hill"),
            ("RSE Crypto", "rse")
        ]

        for i, (name, value) in enumerate(algorithms):
            rb = ttk.Radiobutton(self.right_frame, text=name, variable=self.encryption_type, value=value)
            rb.grid(row=i, column=0, sticky="w", pady=6, padx=20)

        # === Buttons at Bottom ===
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=25)

        self.decrypt_btn = tk.Button(
            button_frame,
            text=self.get_text("decrypt_btn"),
            command=self.decrypt,
            bg="#e74c3c",
            fg="white",
            font=("Arial", 12, "bold"),
            width=22,
            height=2,
            relief="flat",
            cursor="hand2",
            bd=0
        )
        self.decrypt_btn.pack(side=tk.LEFT, padx=20)

        self.encrypt_btn = tk.Button(
            button_frame,
            text=self.get_text("encrypt_btn"),
            command=self.encrypt,
            bg="#27ae60",
            fg="white",
            font=("Arial", 12, "bold"),
            width=22,
            height=2,
            relief="flat",
            cursor="hand2",
            bd=0
        )
        self.encrypt_btn.pack(side=tk.RIGHT, padx=20)

        # Hover effect
        for btn, color in [(self.decrypt_btn, "#c0392b"), (self.encrypt_btn, "#229954")]:
            original_color = btn['bg']
            btn.bind("<Enter>", lambda e, b=btn, c=color: b.config(bg=c))
            btn.bind("<Leave>", lambda e, b=btn, c=original_color: b.config(bg=c))

    def get_params(self):
        lang = "arabic" if self.language.get() == "Arabic" else "english"
        return {
            "algorithm": self.encryption_type.get(),
            "key": self.key_entry.get(),
            "a": self.a_value_entry.get(),
            "plaintext": self.plaintext_entry.get(),
            "ciphertext": self.ciphertext_entry.get(),
            "language": lang
        }

    def encrypt(self):
        params = self.get_params()
        algorithm = params["algorithm"]
        plaintext = params["plaintext"]
        lang = params["language"]

        if not plaintext.strip():
            messagebox.showwarning(self.get_text("warning_title"), self.get_text("encrypt_warning"))
            return

        try:
            if algorithm == "caesar":
                cipher = enc.caesar_encrypt(plaintext, lang=lang)
                key = "3"
                a_val = ""

            elif algorithm == "playfair":
                default_key = "ملك" if lang == "arabic" else "MONARCHY"
                key = params["key"] or default_key
                cipher = enc.playfair_encrypt(plaintext, key, lang=lang)
                a_val = ""

            elif algorithm == "affine":
                a = int(params["a"]) if params["a"] else (5 if lang == "english" else 7)
                b = int(params["key"]) if params["key"] else (8 if lang == "english" else 3)
                cipher = enc.affine_encrypt(plaintext, a, b, lang=lang)
                key = f"a={a}, b={b}"
                a_val = str(a)

            elif algorithm == "vigenere":
                default_key = "مفتاح" if lang == "arabic" else "KEY"
                key = params["key"] or default_key
                cipher = enc.vigenere_encrypt(plaintext, key, lang=lang)
                a_val = ""

            elif algorithm == "hill":
                cipher = enc.hill_encrypt(plaintext, lang=lang)
                key = "مصفوفة 2x2" if lang == "arabic" else "Default 2x2 Matrix"
                a_val = ""

            elif algorithm == "rse":
                default_key = "سري" if lang == "arabic" else "SECRET"
                key = params["key"] or default_key
                cipher = enc.rse_crypto_encrypt(plaintext, key, lang=lang)
                a_val = ""

            else:
                messagebox.showerror(self.get_text("error_title"), self.get_text("unsupported_algorithm"))
                return

            # Display results
            self.ciphertext_entry.delete(0, tk.END)
            self.ciphertext_entry.insert(0, cipher)
            self.key_entry.delete(0, tk.END)
            self.key_entry.insert(0, key)
            self.a_value_entry.delete(0, tk.END)
            self.a_value_entry.insert(0, a_val)
            self.output_text.delete(1.0, tk.END)

            output = self.get_text("encrypt_success").format(cipher, key)
            if a_val:
                if self.language.get() == "Arabic":
                    output += f"\nقيمة A: {a_val}"
                else:
                    output += f"\nValue of A: {a_val}"
            self.output_text.insert(tk.END, output)

        except Exception as e:
            messagebox.showerror(self.get_text("encrypt_error"), self.get_text("error_details").format(str(e)))

    def decrypt(self):
        params = self.get_params()
        algorithm = params["algorithm"]
        ciphertext = params["ciphertext"]
        lang = params["language"]

        if not ciphertext.strip():
            messagebox.showwarning(self.get_text("warning_title"), self.get_text("decrypt_warning"))
            return

        try:
            if algorithm == "caesar":
                plain = enc.caesar_decrypt(ciphertext, lang=lang)
                key = "3"

            elif algorithm == "playfair":
                default_key = "ملك" if lang == "arabic" else "MONARCHY"
                key = params["key"] or default_key
                plain = enc.playfair_decrypt(ciphertext, key, lang=lang)

            elif algorithm == "affine":
                a = int(params["a"]) if params["a"] else (5 if lang == "english" else 7)
                b = int(params["key"]) if params["key"] else (8 if lang == "english" else 3)
                plain = enc.affine_decrypt(ciphertext, a, b, lang=lang)
                key = f"a={a}, b={b}"

            elif algorithm == "vigenere":
                default_key = "مفتاح" if lang == "arabic" else "KEY"
                key = params["key"] or default_key
                plain = enc.vigenere_decrypt(ciphertext, key, lang=lang)

            elif algorithm == "hill":
                plain = enc.hill_decrypt(ciphertext, lang=lang)
                key = "مصفوفة 2x2" if lang == "arabic" else "Default 2x2 Matrix"

            elif algorithm == "rse":
                default_key = "سري" if lang == "arabic" else "SECRET"
                key = params["key"] or default_key
                plain = enc.rse_crypto_decrypt(ciphertext, key, lang=lang)

            else:
                messagebox.showerror(self.get_text("error_title"), self.get_text("unsupported_algorithm"))
                return

            # Display results
            self.plaintext_entry.delete(0, tk.END)
            self.plaintext_entry.insert(0, plain)
            self.output_text.delete(1.0, tk.END)
            output = self.get_text("decrypt_success").format(plain, key)
            self.output_text.insert(tk.END, output)

        except Exception as e:
            messagebox.showerror(self.get_text("decrypt_error"), self.get_text("error_details").format(str(e)))


if __name__ == "__main__":
    root = tk.Tk()
    app = EncryptionApp(root)
    root.mainloop()