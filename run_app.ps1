# تشغيل تطبيق التشفير التعليمي
# Running Encryption App

Write-Host "تشغيل تطبيق التشفير التعليمي..." -ForegroundColor Green
Write-Host "Running Encryption App..." -ForegroundColor Green

# الانتقال إلى مجلد المشروع
Set-Location $PSScriptRoot

# إنشاء البيئة الافتراضية إذا لم تكن موجودة
if (-not (Test-Path ".venv")) {
    Write-Host "إنشاء البيئة الافتراضية..." -ForegroundColor Yellow
    Write-Host "Creating virtual environment..." -ForegroundColor Yellow
    python -m venv .venv
}

# تفعيل البيئة الافتراضية
Write-Host "تفعيل البيئة الافتراضية..." -ForegroundColor Yellow
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
& .venv\Scripts\Activate.ps1

# تثبيت المكتبات المطلوبة
Write-Host "تثبيت المكتبات المطلوبة..." -ForegroundColor Yellow
Write-Host "Installing required packages..." -ForegroundColor Yellow
pip install -r requirements.txt

# تشغيل التطبيق
Write-Host "تشغيل التطبيق..." -ForegroundColor Green
Write-Host "Starting application..." -ForegroundColor Green
python main.py

Read-Host "اضغط Enter للخروج / Press Enter to exit"
