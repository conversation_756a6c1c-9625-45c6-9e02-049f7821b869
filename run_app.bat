@echo off
echo تشغيل تطبيق التشفير التعليمي...
echo Running Encryption App...

cd /d "%~dp0"

if not exist ".venv" (
    echo إنشاء البيئة الافتراضية...
    echo Creating virtual environment...
    python -m venv .venv
)

echo تفعيل البيئة الافتراضية...
echo Activating virtual environment...
call .venv\Scripts\activate.bat

echo تثبيت المكتبات المطلوبة...
echo Installing required packages...
pip install -r requirements.txt

echo تشغيل التطبيق...
echo Starting application...
python main.py

pause
